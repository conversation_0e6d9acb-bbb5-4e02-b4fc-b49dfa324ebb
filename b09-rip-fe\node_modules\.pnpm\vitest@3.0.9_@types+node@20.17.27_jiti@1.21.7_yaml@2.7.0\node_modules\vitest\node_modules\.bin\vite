#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/vite@6.2.3_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../vite/bin/vite.js" "$@"
fi
