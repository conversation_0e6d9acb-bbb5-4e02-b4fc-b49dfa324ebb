hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.2':
    '@adobe/css-tools': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@dnd-kit/accessibility@3.1.1(react@19.0.0)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/utilities@3.2.2(react@19.0.0)':
    '@dnd-kit/utilities': private
  '@esbuild/aix-ppc64@0.25.1':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.1':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.1':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.1':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.1':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.1':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.1':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.1':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.1':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.1':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.1':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.1':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.1':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.1':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.1':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.1':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.1':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.1':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.1':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.1':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.1':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.1':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.1':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.1':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.1':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.5.1(eslint@9.23.0(jiti@1.21.7))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/js@9.23.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.7':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.27.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@next/env@15.2.4':
    '@next/env': private
  '@next/eslint-plugin-next@15.2.4':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@15.2.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.2.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.2.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.2.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.2.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.2.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.2.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.2.4':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.5(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.14(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.4(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.0(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@19.0.12)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.2(@types/react-dom@19.0.4(@types/react@19.0.12))(@types/react@19.0.12)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@rollup/rollup-android-arm-eabi@4.37.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.37.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.37.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.37.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.37.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.37.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.37.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.37.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.37.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.37.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.37.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.37.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.37.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.37.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.37.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.37.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.37.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.37.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.37.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.37.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tanstack/query-core@5.69.0':
    '@tanstack/query-core': private
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/lodash@4.17.16':
    '@types/lodash': private
  '@types/raf@3.4.3':
    '@types/raf': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@typescript-eslint/eslint-plugin@8.28.0(@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2))(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.28.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.28.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.28.0(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.28.0':
    '@typescript-eslint/visitor-keys': private
  '@unrs/rspack-resolver-binding-darwin-arm64@1.3.0':
    '@unrs/rspack-resolver-binding-darwin-arm64': private
  '@unrs/rspack-resolver-binding-darwin-x64@1.3.0':
    '@unrs/rspack-resolver-binding-darwin-x64': private
  '@unrs/rspack-resolver-binding-freebsd-x64@1.3.0':
    '@unrs/rspack-resolver-binding-freebsd-x64': private
  '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm-gnueabihf': private
  '@unrs/rspack-resolver-binding-linux-arm-musleabihf@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm-musleabihf': private
  '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm64-gnu': private
  '@unrs/rspack-resolver-binding-linux-arm64-musl@1.3.0':
    '@unrs/rspack-resolver-binding-linux-arm64-musl': private
  '@unrs/rspack-resolver-binding-linux-ppc64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-ppc64-gnu': private
  '@unrs/rspack-resolver-binding-linux-s390x-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-s390x-gnu': private
  '@unrs/rspack-resolver-binding-linux-x64-gnu@1.3.0':
    '@unrs/rspack-resolver-binding-linux-x64-gnu': private
  '@unrs/rspack-resolver-binding-linux-x64-musl@1.3.0':
    '@unrs/rspack-resolver-binding-linux-x64-musl': private
  '@unrs/rspack-resolver-binding-wasm32-wasi@1.3.0':
    '@unrs/rspack-resolver-binding-wasm32-wasi': private
  '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-arm64-msvc': private
  '@unrs/rspack-resolver-binding-win32-ia32-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-ia32-msvc': private
  '@unrs/rspack-resolver-binding-win32-x64-msvc@1.3.0':
    '@unrs/rspack-resolver-binding-win32-x64-msvc': private
  '@vitest/expect@3.0.9':
    '@vitest/expect': private
  '@vitest/mocker@3.0.9(vite@6.2.3(@types/node@20.17.27)(jiti@1.21.7)(yaml@2.7.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.0.9':
    '@vitest/pretty-format': private
  '@vitest/runner@3.0.9':
    '@vitest/runner': private
  '@vitest/snapshot@3.0.9':
    '@vitest/snapshot': private
  '@vitest/spy@3.0.9':
    '@vitest/spy': private
  '@vitest/utils@3.0.9':
    '@vitest/utils': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-arraybuffer@1.0.2:
    base64-arraybuffer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  btoa@1.2.1:
    btoa: private
  busboy@1.6.0:
    busboy: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001707:
    caniuse-lite: private
  canvg@3.0.11:
    canvg: private
  chai@5.2.0:
    chai: private
  chalk@3.0.0:
    chalk: private
  check-error@2.1.1:
    check-error: private
  chokidar@3.6.0:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@4.1.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  core-js@3.41.0:
    core-js: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-line-break@2.1.0:
    css-line-break: private
  css-select@5.1.0:
    css-select: private
  css-what@6.1.0:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dom-accessibility-api@0.6.3:
    dom-accessibility-api: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  dompurify@3.2.4:
    dompurify: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.123:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  entities@4.5.0:
    entities: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.6.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.1:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.9.1(eslint-plugin-import@2.31.0)(eslint@9.23.0(jiti@1.21.7)):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.9.1)(eslint@9.23.0(jiti@1.21.7)):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.28.0(eslint@9.23.0(jiti@1.21.7))(typescript@5.8.2))(eslint-import-resolver-typescript@3.9.1)(eslint@9.23.0(jiti@1.21.7)):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.23.0(jiti@1.21.7)):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.23.0(jiti@1.21.7)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.4(eslint@9.23.0(jiti@1.21.7)):
    eslint-plugin-react: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@4.0.7:
    eventemitter3: private
  expect-type@1.2.0:
    expect-type: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.2:
    form-data: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  htmlparser2@8.0.2:
    htmlparser2: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@1.3.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  lru-cache@10.4.3:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  playwright-core@1.51.1:
    playwright-core: private
  playwright@1.51.1:
    playwright: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.3):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.3):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.3):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-format@27.5.1:
    pretty-format: private
  prop-types@15.8.1:
    prop-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  raf@3.4.1:
    raf: private
  react-is@18.3.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@19.0.12)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@19.0.12)(react@19.0.0):
    react-remove-scroll: private
  react-smooth@4.0.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@19.0.12)(react@19.0.0):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  redent@3.0.0:
    redent: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rgbcolor@1.0.1:
    rgbcolor: private
  rollup@4.37.0:
    rollup: private
  rspack-resolver@1.3.0:
    rspack-resolver: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.25.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  stable-hash@0.0.5:
    stable-hash: private
  stackback@0.0.2:
    stackback: private
  stackblur-canvas@2.7.0:
    stackblur-canvas: private
  std-env@3.8.1:
    std-env: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.6(react@19.0.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-pathdata@6.0.3:
    svg-pathdata: private
  tabbable@6.2.0:
    tabbable: private
  text-segmentation@1.0.3:
    text-segmentation: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.12:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.8.2):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.19.8:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.0.12)(react@19.0.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.0.12)(react@19.0.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.0.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utrie@1.0.2:
    utrie: private
  victory-vendor@36.9.2:
    victory-vendor: private
  vite-node@3.0.9(@types/node@20.17.27)(jiti@1.21.7)(yaml@2.7.0):
    vite-node: private
  vite@6.2.3(@types/node@20.17.27)(jiti@1.21.7)(yaml@2.7.0):
    vite: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  yaml@2.7.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.5
pendingBuilds: []
prunedAt: Sun, 01 Jun 2025 20:51:30 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/wasi-threads@1.0.1'
  - '@esbuild/aix-ppc64@0.25.1'
  - '@esbuild/android-arm64@0.25.1'
  - '@esbuild/android-arm@0.25.1'
  - '@esbuild/android-x64@0.25.1'
  - '@esbuild/darwin-arm64@0.25.1'
  - '@esbuild/darwin-x64@0.25.1'
  - '@esbuild/freebsd-arm64@0.25.1'
  - '@esbuild/freebsd-x64@0.25.1'
  - '@esbuild/linux-arm64@0.25.1'
  - '@esbuild/linux-arm@0.25.1'
  - '@esbuild/linux-ia32@0.25.1'
  - '@esbuild/linux-loong64@0.25.1'
  - '@esbuild/linux-mips64el@0.25.1'
  - '@esbuild/linux-ppc64@0.25.1'
  - '@esbuild/linux-riscv64@0.25.1'
  - '@esbuild/linux-s390x@0.25.1'
  - '@esbuild/linux-x64@0.25.1'
  - '@esbuild/netbsd-arm64@0.25.1'
  - '@esbuild/netbsd-x64@0.25.1'
  - '@esbuild/openbsd-arm64@0.25.1'
  - '@esbuild/openbsd-x64@0.25.1'
  - '@esbuild/sunos-x64@0.25.1'
  - '@esbuild/win32-arm64@0.25.1'
  - '@esbuild/win32-ia32@0.25.1'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@next/swc-darwin-arm64@15.2.4'
  - '@next/swc-darwin-x64@15.2.4'
  - '@next/swc-linux-arm64-gnu@15.2.4'
  - '@next/swc-linux-arm64-musl@15.2.4'
  - '@next/swc-linux-x64-gnu@15.2.4'
  - '@next/swc-linux-x64-musl@15.2.4'
  - '@next/swc-win32-arm64-msvc@15.2.4'
  - '@rollup/rollup-android-arm-eabi@4.37.0'
  - '@rollup/rollup-android-arm64@4.37.0'
  - '@rollup/rollup-darwin-arm64@4.37.0'
  - '@rollup/rollup-darwin-x64@4.37.0'
  - '@rollup/rollup-freebsd-arm64@4.37.0'
  - '@rollup/rollup-freebsd-x64@4.37.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.37.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.37.0'
  - '@rollup/rollup-linux-arm64-gnu@4.37.0'
  - '@rollup/rollup-linux-arm64-musl@4.37.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.37.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.37.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.37.0'
  - '@rollup/rollup-linux-riscv64-musl@4.37.0'
  - '@rollup/rollup-linux-s390x-gnu@4.37.0'
  - '@rollup/rollup-linux-x64-gnu@4.37.0'
  - '@rollup/rollup-linux-x64-musl@4.37.0'
  - '@rollup/rollup-win32-arm64-msvc@4.37.0'
  - '@rollup/rollup-win32-ia32-msvc@4.37.0'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/rspack-resolver-binding-darwin-arm64@1.3.0'
  - '@unrs/rspack-resolver-binding-darwin-x64@1.3.0'
  - '@unrs/rspack-resolver-binding-freebsd-x64@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm-musleabihf@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-arm64-musl@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-ppc64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-s390x-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-x64-gnu@1.3.0'
  - '@unrs/rspack-resolver-binding-linux-x64-musl@1.3.0'
  - '@unrs/rspack-resolver-binding-wasm32-wasi@1.3.0'
  - '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.3.0'
  - '@unrs/rspack-resolver-binding-win32-ia32-msvc@1.3.0'
  - fsevents@2.3.2
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm
virtualStoreDirMaxLength: 60
