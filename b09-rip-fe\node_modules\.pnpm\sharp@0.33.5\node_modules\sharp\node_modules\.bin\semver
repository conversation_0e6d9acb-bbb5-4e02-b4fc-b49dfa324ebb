#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules/semver/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules/semver/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules/semver/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules/semver/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/semver@7.7.1/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../semver/bin/semver.js" "$@"
else
  exec node  "$basedir/../../../semver/bin/semver.js" "$@"
fi
