@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\vitest@3.0.9_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0\node_modules\vitest\node_modules;C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\vitest@3.0.9_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0\node_modules;C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\vitest@3.0.9_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0\node_modules\vitest\node_modules;C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\vitest@3.0.9_@types+node@20.17.27_jiti@1.21.7_yaml@2.7.0\node_modules;C:\Users\<USER>\UAT Propen\b09-rip-fe\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
