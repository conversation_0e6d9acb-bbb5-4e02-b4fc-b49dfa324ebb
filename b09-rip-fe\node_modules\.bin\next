#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules:/mnt/c/Users/<USER>/UAT Propen/b09-rip-fe/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
